# -*- coding: utf-8 -*-
"""
XhsDetailUpdater 测试示例
演示如何使用 XhsDetailUpdater 类进行小红书作品详情更新
"""

import sys
import os

# 添加项目路径到 sys.path
cur_path = os.path.dirname(os.path.abspath(__file__))
sys.path.append(cur_path)

from scene.xhs_detail_updater import XhsDetailUpdater
from client.service_client import Environment
from common.logger import Logger, INFO
from utils.file_utils import FileUtils


def test_xhs_detail_updater():
    """测试 XhsDetailUpdater 的基本功能"""
    
    # 初始化日志记录器
    logger = Logger(FileUtils.get_project_dir(), 'xhs_detail_updater_test.log', INFO).log()
    
    # 创建更新器实例
    updater = XhsDetailUpdater(
        user_id=9,
        api_token="api-token-a8cabb2bf81f04c8284c63db01514d0f",
        logger_=logger,
        environment=Environment.LOCAL  # 使用本地环境进行测试
    )
    
    print("=== XhsDetailUpdater 测试开始 ===")
    print(f"用户ID: {updater.user_id}")
    print(f"API环境: {updater.service_client.environment.value}")
    print(f"基础URL: {updater.service_client.base_url}")
    
    # 执行更新操作
    print("\n=== 开始执行更新操作 ===")
    try:
        result = updater.performing_updates()
        
        if result:
            print("✅ 更新操作执行成功!")
        else:
            print("❌ 更新操作执行失败或无任务需要处理")
            
    except Exception as e:
        print(f"❌ 更新操作发生异常: {e}")
        import traceback
        traceback.print_exc()


def test_service_client_integration():
    """测试 ServiceClient 集成"""
    
    logger = Logger(FileUtils.get_project_dir(), 'service_client_integration_test.log', INFO).log()
    
    updater = XhsDetailUpdater(
        user_id=9,
        api_token="api-token-a8cabb2bf81f04c8284c63db01514d0f",
        logger_=logger,
        environment=Environment.LOCAL
    )
    
    print("\n=== ServiceClient 集成测试 ===")
    
    # 测试查询可更新任务
    print("1. 测试查询可更新任务...")
    try:
        response = updater.service_client.query_can_update(
            user_id=updater.user_id,
            platform="xhs"
        )
        
        if response:
            print(f"✅ 查询成功: {response}")
            
            if updater.service_client.is_response_success(response):
                data = updater.service_client.get_response_data(response)
                print(f"获取到 {len(data) if data else 0} 个任务")
            else:
                print(f"❌ 查询失败: {response.get('msg', '未知错误')}")
        else:
            print("❌ 查询失败: 响应为空")
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")
    
    # 测试标记更新（使用示例ID）
    print("\n2. 测试标记更新...")
    try:
        mark_response = updater.service_client.mark_update(record_id=999)  # 使用测试ID
        
        if mark_response:
            print(f"✅ 标记请求成功: {mark_response}")
        else:
            print("❌ 标记请求失败: 响应为空")
            
    except Exception as e:
        print(f"❌ 标记请求异常: {e}")


def test_batch_id_generation():
    """测试批次ID生成"""
    
    logger = Logger(FileUtils.get_project_dir(), 'batch_id_test.log', INFO).log()
    
    updater = XhsDetailUpdater(
        user_id=9,
        api_token="test-token",
        logger_=logger,
        environment=Environment.LOCAL
    )
    
    print("\n=== 批次ID生成测试 ===")
    
    # 生成多个批次ID
    batch_ids = []
    for i in range(3):
        batch_id = updater._generate_row_id()
        batch_ids.append(batch_id)
        print(f"批次ID {i+1}: {batch_id}")
        
        # 稍微延迟确保ID不同
        import time
        time.sleep(0.001)
    
    # 检查ID是否唯一
    if len(set(batch_ids)) == len(batch_ids):
        print("✅ 所有批次ID都是唯一的")
    else:
        print("❌ 发现重复的批次ID")


if __name__ == "__main__":
    print("XhsDetailUpdater 测试开始...")
    
    try:
        # test_batch_id_generation()
        # test_service_client_integration()
        test_xhs_detail_updater()
        print("\n✅ 所有测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
